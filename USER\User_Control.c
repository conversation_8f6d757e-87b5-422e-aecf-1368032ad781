#include "User_Control.h"
#include "User_IR_Sensor.h"
#include "User_Parameter.h"
#include "key.h"
#include "counting.h"

extern unsigned char Mode;

unsigned int Line_Out_timesStik=LINE_OUT_TIME;
unsigned char Line_Out_Flag=0;

unsigned int SysTime_Stick=0;


void Control(void)
{

//	Transform_Ultrasonic();
	if(SysTime_Stick>0)	SysTime_Stick--;
	ADC_Transform(); 	//二值化处理函数,并计算误差
	IR_Identification();//特征检测	
	if(Mode==0xA1) //启动模式
	{
		PID_Out=Error*PID_P+Sum_Error*PID_I+(Error-Last_Error)*PID_D;	//PID计算
		Sum_Error+=Error;
		Last_Error=Error; 
		PID_Out=PID_Out/10;
		Set_Motor((Speed-PID_Out)*30,(Speed+PID_Out)*60);	//控制电机速度
	}
	else //非启动模式
	{
		Check_Key();    //检查按键状态
		Set_Motor(0,0);	//电机停止
	}	
}
void IR_Identification(void)
{
//检测出线
	if(IR_RES==0x00||IR_RES==0x7E)
	{
		 if(Line_Out_timesStik>0) Line_Out_timesStik--;
		 else	   {Line_Out_Flag=1;}
	}
	else
	{
		Line_Out_timesStik=LINE_OUT_TIME;
	}
}





